<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/error}">
<head>
    <title>오류 발생 - 한림공원 QR 체험</title>
    <meta name="description" content="요청 처리 중 오류가 발생했습니다.">
</head>
<body>
    <div layout:fragment="content">
        <!-- 에러 아이콘 -->
        <i class="fas fa-exclamation-circle text-danger error-icon"></i>

        <!-- 에러 코드 (동적) -->
        <div class="error-code text-danger" th:text="${errorCode != null ? errorCode : 'ERROR'}">ERROR</div>

        <!-- 에러 메시지 -->
        <h1 class="error-title">오류가 발생했습니다</h1>
        <p class="error-description" th:text="${errorMessage != null ? errorMessage : '요청 처리 중 예상치 못한 오류가 발생했습니다.'}">
            요청 처리 중 예상치 못한 오류가 발생했습니다.
        </p>

        <!-- 요청 URL 표시 (개발 환경에서만) -->
        <div class="error-details" th:if="${requestUrl != null}">
            <strong>요청 URL:</strong> <span th:text="${requestUrl}"></span>
        </div>

        <!-- 에러 상세 정보 (개발 환경에서만) -->
        <div class="error-details" th:if="${detail != null and #strings.length(detail) > 0}">
            <details>
                <summary style="cursor: pointer; color: #6c757d;">상세 정보 보기</summary>
                <pre style="text-align: left; font-size: 0.8rem; margin-top: 1rem; white-space: pre-wrap;" th:text="${detail}"></pre>
            </details>
        </div>

        <!-- 액션 버튼들 -->
        <div class="error-actions">
            <button type="button" class="btn btn-primary" onclick="reloadPage()">
                다시 시도하기
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="goBack()">
                뒤로가기
            </button>
            <a th:href="@{/}" class="btn btn-outline-primary">
                홈으로 돌아가기
            </a>
        </div>
    </div>
</body>
</html>

<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/manage}" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <title th:text="${isEdit ? '문제 카테고리 수정' : '문제 카테고리 등록'} + ' - 한림공원 관리시스템'">문제 카테고리 등록 - 한림공원 관리시스템</title>
    <meta name="description" th:content="${isEdit ? '문제 카테고리 정보를 수정합니다.' : '새로운 문제 카테고리를 등록합니다.'}">
</head>
<th:block layout:fragment="head">
    <link href="/css/manage/content/quiz-category.css" rel="stylesheet">
</th:block>
<body>
    <div layout:fragment="content">
        <!-- 페이지 헤더 -->
        <section class="page-header">
            <div class="container">
                <div class="row">
                    <div class="col-md-8">
                        <h2 th:text="${isEdit ? '카테고리 수정' : '카테고리 등록'}">카테고리 등록</h2>
                        <p th:text="${isEdit ? '카테고리 정보를 수정합니다.' : '새로운 카테고리를 등록합니다.'}">새로운 카테고리를 등록합니다.</p>
                    </div>
                    <div class="col-md-4">
                        <a th:href="@{/manage/quiz-category}" class="btn btn-outline-secondary">
                            목록으로 돌아가기
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- 메인 콘텐츠 -->
        <section>
            <div class="container">
                <!-- 성공 메시지 -->
                <div th:if="${successMessage}" class="alert alert-success" role="alert">
                    <span th:text="${successMessage}">성공 메시지</span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <!-- 에러 메시지 -->
                <div th:if="${errorMessage}" class="alert alert-danger" role="alert">
                    <span th:text="${errorMessage}">에러 메시지</span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <div class="form-container">
                    <div class="card form-card">
                        <div class="form-header">
                            <h3 th:text="${isEdit ? '카테고리 정보 수정' : '새 카테고리 등록'}">새 카테고리 등록</h3>
                        </div>
                        
                        <div class="form-body">
                            <form id="categoryForm" th:data-is-edit="${isEdit}"
                                  th:data-category-id="${isEdit ? quizCategory.categoryId : ''}"
                                  method="POST">
                                
                                <!-- 카테고리명 입력 -->
                                <div class="mb-4">
                                    <label for="categoryName" class="form-label">
                                        카테고리명 <span class="required">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="categoryName" name="categoryName"
                                           data-category-input required maxlength="100"
                                           placeholder="카테고리명을 입력하세요"
                                           th:value="${isEdit ? quizCategory.categoryName : ''}">
                                    <div class="form-text">최대 100자까지 입력 가능합니다.</div>
                                    <div class="invalid-feedback">
                                        카테고리명을 입력해주세요.
                                    </div>
                                </div>

                                <!-- 카테고리 설명 -->
                                <div class="mb-4">
                                    <label for="description" class="form-label">카테고리 설명</label>
                                    <textarea class="form-control" id="description" name="description" rows="4"
                                              data-description-input
                                              placeholder="카테고리에 대한 설명을 입력하세요 (선택사항)"
                                              th:text="${isEdit ? quizCategory.description : ''}"></textarea>
                                    <div class="form-text">최대 1000자까지 입력 가능합니다.</div>
                                    <div class="invalid-feedback">
                                        설명은 1000자를 초과할 수 없습니다.
                                    </div>
                                </div>



                                <!-- 버튼 그룹 -->
                                <div class="button-group">
                                    <a th:href="@{/manage/quiz-category}" class="btn btn-outline-secondary btn-cancel" data-cancel-btn>
                                        취소
                                    </a>
                                    <button type="submit" class="btn btn-primary btn-submit" id="submitBtn" data-submit-btn>
                                        <span th:text="${isEdit ? '수정하기' : '등록하기'}">등록하기</span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- 추가 스크립트 -->
    <th:block layout:fragment="script">
        <script th:inline="javascript">
            document.addEventListener('DOMContentLoaded', function() {
                const form = document.getElementById('categoryForm');
                const categoryNameInput = document.querySelector('[data-category-input]');
                const descriptionTextarea = document.querySelector('[data-description-input]');
                const submitBtn = document.getElementById('submitBtn');

                const isEdit = form.getAttribute('data-is-edit') === 'true';
                const categoryId = form.getAttribute('data-category-id');

                // 카테고리명 유효성 검사
                if (categoryNameInput) {
                    categoryNameInput.addEventListener('input', function() {
                        const value = this.value.trim();
                        if (value.length === 0) {
                            this.setCustomValidity('카테고리명을 입력해주세요.');
                        } else if (value.length > 100) {
                            this.setCustomValidity('카테고리명은 100자를 초과할 수 없습니다.');
                        } else {
                            this.setCustomValidity('');
                        }
                    });
                }

                // 설명 유효성 검사
                if (descriptionTextarea) {
                    descriptionTextarea.addEventListener('input', function() {
                        const value = this.value;
                        if (value.length > 1000) {
                            this.setCustomValidity('설명은 1000자를 초과할 수 없습니다.');
                        } else {
                            this.setCustomValidity('');
                        }
                    });
                }

                // 폼 제출 처리
                form.addEventListener('submit', function(e) {
                    e.preventDefault();

                    // 유효성 검사
                    if (!form.checkValidity()) {
                        form.classList.add('was-validated');
                        return;
                    }

                    // 버튼 비활성화
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = isEdit ? '수정 중...' : '등록 중...';

                    // 폼 데이터 수집
                    const formData = {
                        categoryName: categoryNameInput.value.trim(),
                        description: descriptionTextarea.value.trim()
                    };

                    // AJAX 요청 설정
                    const ajaxConfig = {
                        url: isEdit ? `/manage/quiz-category/${categoryId}` : '/manage/quiz-category/add',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify(formData),
                        beforeSend: function (xhr) { xhr.setRequestHeader( [[${_csrf.headerName}]], [[${_csrf.token}]] ); },
                        success: function(response) {
                            const message = isEdit ? '카테고리가 성공적으로 수정되었습니다.' : '카테고리가 성공적으로 등록되었습니다.';
                            alert(message);
                            window.location.href = '/manage/quiz-category';
                        },
                        error: function(xhr, status, error) {
                            console.error('Form submission error:', error);
                            let errorMessage = isEdit ? '카테고리 수정 중 오류가 발생했습니다.' : '카테고리 등록 중 오류가 발생했습니다.';

                            if (xhr.responseJSON && xhr.responseJSON.message) {
                                errorMessage = xhr.responseJSON.message;
                            } else if (xhr.responseText) {
                                try {
                                    const errorData = JSON.parse(xhr.responseText);
                                    if (errorData.message) {
                                        errorMessage = errorData.message;
                                    }
                                } catch (e) {
                                    // JSON 파싱 실패시 기본 메시지 사용
                                }
                            }

                            alert('오류: ' + errorMessage);

                            // 버튼 복원
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = isEdit ? '수정하기' : '등록하기';
                        }
                    };

                    // AJAX 요청 실행
                    $.ajax(ajaxConfig);
                });
            });
        </script>
    </th:block>
</body>
</html>

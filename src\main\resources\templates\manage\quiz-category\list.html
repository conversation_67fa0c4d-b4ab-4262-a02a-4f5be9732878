<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/manage}" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <title>문제 카테고리 관리 - 한림공원 관리시스템</title>
    <meta name="description" content="문제 카테고리를 관리합니다.">
</head>
<th:block layout:fragment="head">
    <link href="/css/manage/content/quiz-category.css" rel="stylesheet">
</th:block>
<body>
    <div layout:fragment="content">
        <!-- 페이지 헤더 -->
        <section class="page-header">
            <div class="container">
                <div class="row">
                    <div class="col-md-8">
                        <h2>콘텐츠 카테고리 관리</h2>
                        <p>문제의 카테고리를 관리합니다.</p>
                    </div>
                    <div class="col-md-4">
                        <a th:href="@{/manage/quiz-category/new}" class="btn btn-primary">
                            새 카테고리 등록
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- 메인 콘텐츠 -->
        <section>
            <div class="container">
                <!-- 성공 메시지 -->
                <div th:if="${successMessage}" class="alert alert-success" role="alert">
                    <span th:text="${successMessage}">성공 메시지</span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <!-- 에러 메시지 -->
                <div th:if="${errorMessage}" class="alert alert-danger" role="alert">
                    <span th:text="${errorMessage}">에러 메시지</span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <!-- 카테고리 목록 -->
                <div class="row">
                    <!-- 카테고리가 있는 경우 -->
                    <div th:if="${quizCategories != null and !quizCategories.isEmpty()}" class="col-12">
                        <div class="row" id="categoryList">
                            <div th:each="category : ${quizCategories}" class="col-lg-4 col-md-6">
                                <div class="card category-card" data-category-card th:data-quiz-category-id="${category.categoryId}">
                                    <div class="card-content">
                                        <div class="category-header">
                                            <h5 class="category-name" data-quiz-category-name th:text="${category.categoryName}">카테고리명</h5>
                                            <div class="dropdown">
                                                <button class="btn btn-outline-secondary dropdown-toggle" type="button"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                    ⋮
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" th:href="@{/manage/quiz-category/{id}/edit(id=${category.categoryId})}">
                                                            수정
                                                        </a>
                                                    </li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <button class="dropdown-item"
                                                                th:data-delete-btn="${category.categoryId}"
                                                                th:data-quiz-category-id="${category.categoryId}"
                                                                th:data-quiz-category-name="${category.categoryName}"
                                                                onclick="deleteCategory(this)">
                                                            삭제
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>

                                        <p class="category-description" data-category-description
                                           th:text="${category.description != null and !category.description.isEmpty() ? category.description : '설명이 없습니다.'}">
                                            카테고리 설명
                                        </p>

                                        <div class="category-footer">
                                            <span>
                                                ID: <span th:text="${category.categoryId}" data-quiz-category-id-display>1</span>
                                            </span>
                                            <div>
                                                <a th:href="@{/manage/quiz-category/{id}/edit(id=${category.categoryId})}"
                                                   class="btn btn-outline-primary btn-action" data-edit-btn>
                                                    수정
                                                </a>
                                                <button class="btn btn-outline-danger btn-action"
                                                        th:data-delete-btn-small="${category.categoryId}"
                                                        th:data-quiz-category-id="${category.categoryId}"
                                                        th:data-quiz-category-name="${category.categoryName}"
                                                        onclick="deleteCategory(this)">
                                                    삭제
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 카테고리가 없는 경우 -->
                    <div th:if="${quizCategories == null or quizCategories.isEmpty()}" class="col-12">
                        <div class="empty-state">
                            <h3>등록된 문제 카테고리가 없습니다</h3>
                            <p class="mb-4">새로운 문제 카테고리를 등록해보세요.</p>
                            <a th:href="@{/manage/quiz-category/new}" class="btn btn-primary">
                                첫 번째 카테고리 등록하기
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- 추가 스크립트 -->
    <th:block layout:fragment="script">
        <script>
            function deleteCategory(button) {
                const quizCategoryId = button.getAttribute('data-quiz-category-id');
                const quizCategoryName = button.getAttribute('data-quiz-category-name');

                if (!confirm(`'${quizCategoryName}' 카테고리를 정말 삭제하시겠습니까?\n\n이 작업은 되돌릴 수 없습니다.`)) {
                    return;
                }

                // 삭제 폼 생성 및 제출
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/manage/quiz-category/${quizCategoryId}/delete`;

                // CSRF 토큰 추가 (필요한 경우)
                const csrfToken = document.querySelector('meta[name="_csrf"]');
                const csrfHeader = document.querySelector('meta[name="_csrf_header"]');
                if (csrfToken && csrfHeader) {
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = '_token';
                    csrfInput.value = csrfToken.getAttribute('content');
                    form.appendChild(csrfInput);
                }

                document.body.appendChild(form);
                form.submit();
            }
        </script>
    </th:block>
</body>
</html>

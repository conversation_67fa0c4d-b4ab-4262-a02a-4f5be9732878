<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/manage}" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <title>문제 카테고리 관리 - 한림공원 관리시스템</title>
    <meta name="description" content="문제 카테고리를 관리합니다.">
</head>
<th:block layout:fragment="head">
    <link href="/css/manage/content/quiz-category.css" rel="stylesheet">
</th:block>
<body>
    <div layout:fragment="content">
        <!-- 페이지 헤더 -->
        <section class="page-header">
            <div class="container">
                <div class="row">
                    <div class="col-md-8">
                        <h2>콘텐츠 카테고리 관리</h2>
                        <p>문제의 카테고리를 관리합니다.</p>
                    </div>
                    <div class="col-md-4">
                        <a th:href="@{/manage/quiz-category/new}" class="btn btn-primary">
                            새 카테고리 등록
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- 메인 콘텐츠 -->
        <section>
            <div class="container">
                <!-- 성공 메시지 -->
                <div th:if="${successMessage}" class="alert alert-success" role="alert">
                    <span th:text="${successMessage}">성공 메시지</span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <!-- 에러 메시지 -->
                <div th:if="${errorMessage}" class="alert alert-danger" role="alert">
                    <span th:text="${errorMessage}">에러 메시지</span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <!-- 카테고리 목록 -->
                <div class="row">
                    <!-- 카테고리가 있는 경우 -->
                    <div th:if="${quizCategories != null and !quizCategories.isEmpty()}" class="col-12">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th scope="col" style="width: 80px;">ID</th>
                                        <th scope="col" style="width: 200px;">카테고리명</th>
                                        <th scope="col">설명</th>
                                        <th scope="col" style="width: 120px;">관리</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="category : ${quizCategories}"
                                        th:data-quiz-category-id="${category.categoryId}">
                                        <td>
                                            <span class="badge bg-secondary" th:text="${category.categoryId}">1</span>
                                        </td>
                                        <td>
                                            <strong th:text="${category.categoryName}">카테고리명</strong>
                                        </td>
                                        <td>
                                            <span th:text="${category.description != null and !category.description.isEmpty() ? category.description : '설명이 없습니다.'}"
                                                  class="text-muted">카테고리 설명</span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a th:href="@{/manage/quiz-category/{id}/edit(id=${category.categoryId})}"
                                                   class="btn btn-outline-primary btn-sm">
                                                    수정
                                                </a>
                                                <button class="btn btn-outline-danger btn-sm"
                                                        th:data-quiz-category-id="${category.categoryId}"
                                                        th:data-quiz-category-name="${category.categoryName}"
                                                        onclick="deleteCategory(this)">
                                                    삭제
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 카테고리가 없는 경우 -->
                    <div th:if="${quizCategories == null or quizCategories.isEmpty()}" class="col-12">
                        <div class="empty-state">
                            <h3>등록된 문제 카테고리가 없습니다</h3>
                            <p class="mb-4">새로운 문제 카테고리를 등록해보세요.</p>
                            <a th:href="@{/manage/quiz-category/new}" class="btn btn-primary">
                                첫 번째 카테고리 등록하기
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- 추가 스크립트 -->
    <th:block layout:fragment="script">
        <script>
            function deleteCategory(button) {
                const quizCategoryId = button.getAttribute('data-quiz-category-id');
                const quizCategoryName = button.getAttribute('data-quiz-category-name');

                if (!confirm(`'${quizCategoryName}' 카테고리를 정말 삭제하시겠습니까?\n\n이 작업은 되돌릴 수 없습니다.`)) {
                    return;
                }

                // 버튼 비활성화
                button.disabled = true;
                button.textContent = '삭제 중...';

                // CSRF 토큰 가져오기
                const csrfToken = document.querySelector('meta[name="_csrf"]');
                const csrfHeader = document.querySelector('meta[name="_csrf_header"]');

                // AJAX 요청
                $.ajax({
                    url: `/manage/quiz-category/api/${quizCategoryId}`,
                    type: 'DELETE',
                    beforeSend: function(xhr) {
                        if (csrfToken && csrfHeader) {
                            xhr.setRequestHeader(csrfHeader.getAttribute('content'), csrfToken.getAttribute('content'));
                        }
                    },
                    success: function(response) {
                        if (response.success) {
                            alert('카테고리가 성공적으로 삭제되었습니다.');
                            // 해당 행 제거
                            const row = button.closest('tr');
                            row.remove();

                            // 테이블이 비어있으면 빈 상태 표시
                            const tbody = document.querySelector('tbody');
                            if (tbody.children.length === 0) {
                                location.reload();
                            }
                        } else {
                            alert('삭제 실패: ' + (response.message || '알 수 없는 오류가 발생했습니다.'));
                            // 버튼 복원
                            button.disabled = false;
                            button.textContent = '삭제';
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Delete error:', error);
                        let errorMessage = '카테고리 삭제 중 오류가 발생했습니다.';

                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }

                        alert('삭제 실패: ' + errorMessage);

                        // 버튼 복원
                        button.disabled = false;
                        button.textContent = '삭제';
                    }
                });
            }
        </script>
    </th:block>
</body>
</html>
